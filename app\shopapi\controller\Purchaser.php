<?php
namespace app\shopapi\controller;

use app\common\basics\ShopApi;
use app\shopapi\logic\PurchaserLogic;
use app\common\server\JsonServer;

/**
 * 采购人员相关接口
 * Class Purchaser
 * @package app\shopapi\controller
 */
class Purchaser extends ShopApi
{
    /**
     * 获取商家分配的采购人员列表
     * @return \think\response\Json
     */
    public function lists()
    {
        $get = $this->request->get();
        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 20;
        
        $result = PurchaserLogic::getShopPurchaserList($this->shop_id, $page, $limit,$get);
        
        if ($result === false) {
            return JsonServer::error(PurchaserLogic::getError());
        }
        
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 获取商家采购人员分配统计
     * @return \think\response\Json
     */
    public function stats()
    {
        $result = PurchaserLogic::getShopAllocationStats($this->shop_id);
        
        if ($result === false) {
            return JsonServer::error(PurchaserLogic::getError());
        }
        
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 获取商家群发信息限制信息
     * @return \think\response\Json
     */
    public function massMessageLimit()
    {
        $result = PurchaserLogic::getMassMessageLimit($this->shop_id);
        
        if ($result === false) {
            return JsonServer::error(PurchaserLogic::getError());
        }
        
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 检查今日群发信息是否超限
     * @return \think\response\Json
     */
    public function checkMassMessageLimit()
    {
        $result = PurchaserLogic::checkMassMessageLimit($this->shop_id);
        
        return JsonServer::success('检查完成', [
            'can_send' => $result,
            'message' => $result ? '可以发送' : '今日群发次数已达上限'
        ]);
    }

    /**
     * 记录群发信息发送
     * @return \think\response\Json
     */
    public function recordMassMessage()
    {
        $post = $this->request->post();
        
        if (empty($post['target_user_ids']) || !is_array($post['target_user_ids'])) {
            return JsonServer::error('目标用户列表不能为空');
        }
        
        
        $result = PurchaserLogic::recordMassMessage($this->shop_id, $post);
        
        if ($result === false) {
            return JsonServer::error(PurchaserLogic::getError());
        }
        
        return JsonServer::success('记录成功', $result);
    }

    /**
     * 获取群发信息发送记录
     * @return \think\response\Json
     */
    public function massMessageRecords()
    {
        $get = $this->request->get();
        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 20;
        
        $result = PurchaserLogic::getMassMessageRecords($this->shop_id, $page, $limit);
        
        if ($result === false) {
            return JsonServer::error(PurchaserLogic::getError());
        }
        
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 手动重新分配采购人员（需要权限）
     * @return \think\response\Json
     */
    public function reAllocate()
    {
        // 这里可以添加权限检查
        $result = PurchaserLogic::manualReAllocate($this->shop_id);
        
        if ($result === false) {
            return JsonServer::error(PurchaserLogic::getError());
        }
        
        return JsonServer::success('重新分配成功');
    }

    /**
     * 获取采购人员详情
     * @return \think\response\Json
     */
    public function detail()
    {
        $userId = $this->request->get('user_id');
        
        if (empty($userId)) {
            return JsonServer::error('用户ID不能为空');
        }
        
        $result = PurchaserLogic::getPurchaserDetail($this->shop_id, $userId);
        
        if ($result === false) {
            return JsonServer::error(PurchaserLogic::getError());
        }
        
        return JsonServer::success('获取成功', $result);
    }
}

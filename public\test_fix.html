<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信订单API修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button.danger {
            background-color: #f44336;
        }
        .test-button.danger:hover {
            background-color: #da190b;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .fix-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .fix-success {
            background-color: #d4edda;
            color: #155724;
        }
        .fix-pending {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 微信订单API修复验证</h1>
        <p>此页面用于验证微信订单API对接功能的修复情况。</p>
    </div>

    <div class="container">
        <h2>📋 修复内容总结</h2>
        <div class="test-section">
            <h3>已修复的问题</h3>
            <ul>
                <li><span class="fix-status fix-success">✅ 已修复</span> 商家端发货接口 `/shop/order.order/deliveryHandle.html` 未触发微信发货上传</li>
                <li><span class="fix-status fix-success">✅ 已修复</span> 用户端确认收货接口 `/api/order/confirm` 状态未同步到微信</li>
                <li><span class="fix-status fix-success">✅ 已修复</span> 售后退款接口 `/shop/after_sale.after_sale/confirm.html` 参数类型错误</li>
                <li><span class="fix-status fix-success">✅ 已修复</span> 对象和数组类型转换问题</li>
                <li><span class="fix-status fix-success">✅ 已修复</span> 商家端虚拟商品发货微信同步</li>
                <li><span class="fix-status fix-success">✅ 已修复</span> 商家端确认收货微信同步</li>
                <li><span class="fix-status fix-success">✅ 已修复</span> 自提核销微信同步</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🧪 快速验证测试</h2>
        
        <div class="test-section">
            <h3>1. 测试用户确认收货</h3>
            <div class="form-group">
                <label for="user-confirm-order-id">订单ID:</label>
                <input type="number" id="user-confirm-order-id" placeholder="请输入微信支付的订单ID">
            </div>
            <button class="test-button" onclick="testUserConfirm()">测试用户确认收货</button>
            <div id="user-confirm-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试商家发货</h3>
            <div class="form-group">
                <label for="shop-delivery-order-id">订单ID:</label>
                <input type="number" id="shop-delivery-order-id" placeholder="请输入待发货的订单ID">
            </div>
            <button class="test-button" onclick="testShopDelivery()">测试商家发货同步</button>
            <div id="shop-delivery-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试退款同步</h3>
            <div class="form-group">
                <label for="refund-test-order-id">订单ID:</label>
                <input type="number" id="refund-test-order-id" placeholder="请输入需要测试退款的订单ID">
            </div>
            <button class="test-button danger" onclick="testRefundSync()">测试退款同步</button>
            <div id="refund-test-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 检查微信配置</h3>
            <button class="test-button" onclick="checkConfig()">检查微信配置状态</button>
            <div id="config-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📝 使用说明</h2>
        <div class="test-section">
            <h3>测试步骤</h3>
            <ol>
                <li><strong>准备测试订单</strong>：确保有微信支付的订单用于测试</li>
                <li><strong>检查配置</strong>：先点击"检查微信配置状态"确保配置正确</li>
                <li><strong>测试发货</strong>：使用待发货的订单测试发货同步</li>
                <li><strong>测试确认收货</strong>：使用已发货的订单测试确认收货同步</li>
                <li><strong>测试退款</strong>：谨慎测试退款功能（会实际触发退款流程）</li>
            </ol>
            
            <h3>注意事项</h3>
            <ul>
                <li>只有微信支付的订单才会触发微信API同步</li>
                <li>用户必须在小程序中授权才能获取openid</li>
                <li>退款测试会实际触发退款流程，请谨慎操作</li>
                <li>如果同步失败，请检查日志文件获取详细错误信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示结果的通用函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // 发送API请求的通用函数
        async function sendRequest(url, data = {}, method = 'POST') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }

        // 测试用户确认收货
        async function testUserConfirm() {
            const orderId = document.getElementById('user-confirm-order-id').value;

            if (!orderId) {
                showResult('user-confirm-result', '请输入订单ID', 'error');
                return;
            }

            showResult('user-confirm-result', '正在测试用户确认收货...', 'info');

            const result = await sendRequest('/debug/confirm', {
                order_id: orderId
            });

            if (result.code === 1) {
                const data = result.data;
                const message = `测试结果：${data.sync_result}\n` +
                              `订单ID：${data.order_id}\n` +
                              `支付方式：${data.is_wechat_pay ? '微信支付' : '非微信支付'}\n` +
                              `用户OpenID：${data.has_openid ? '已获取' : '未获取'}\n` +
                              `详细信息：${data.sync_detail || '无'}`;
                
                showResult('user-confirm-result', message, data.sync_result.includes('成功') ? 'success' : 'error');
            } else {
                showResult('user-confirm-result', `测试失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 测试商家发货
        async function testShopDelivery() {
            const orderId = document.getElementById('shop-delivery-order-id').value;

            if (!orderId) {
                showResult('shop-delivery-result', '请输入订单ID', 'error');
                return;
            }

            showResult('shop-delivery-result', '正在测试商家发货同步...', 'info');

            const result = await sendRequest('/debug/delivery', {
                order_id: orderId
            });

            if (result.code === 1) {
                const data = result.data;
                const message = `测试结果：${data.sync_result}\n` +
                              `订单ID：${data.order_id}\n` +
                              `支付方式：${data.is_wechat_pay ? '微信支付' : '非微信支付'}\n` +
                              `用户OpenID：${data.has_openid ? '已获取' : '未获取'}\n` +
                              `配送方式：${data.delivery_type}\n` +
                              `详细信息：${data.sync_detail || '无'}`;
                
                showResult('shop-delivery-result', message, data.sync_result.includes('成功') ? 'success' : 'error');
            } else {
                showResult('shop-delivery-result', `测试失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 测试退款同步
        async function testRefundSync() {
            const orderId = document.getElementById('refund-test-order-id').value;

            if (!orderId) {
                showResult('refund-test-result', '请输入订单ID', 'error');
                return;
            }

            if (!confirm('注意：这将实际触发退款流程！确定要继续吗？')) {
                return;
            }

            showResult('refund-test-result', '正在测试退款同步...', 'info');

            const result = await sendRequest('/debug/refund-sync', {
                order_id: orderId,
                refund_amount: 0.01,
                refund_reason: '测试退款'
            });

            if (result.code === 1) {
                showResult('refund-test-result', 
                    `退款同步测试成功！\n订单ID：${result.data.order_id}\n退款金额：${result.data.refund_amount}元`, 
                    'success'
                );
            } else {
                showResult('refund-test-result', `测试失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 检查配置
        async function checkConfig() {
            showResult('config-result', '正在检查微信配置...', 'info');

            const result = await sendRequest('/debug/wechat-config', {}, 'GET');

            if (result.code === 1) {
                const data = result.data;
                const message = `配置检查结果：\n` +
                              `小程序AppID：${data.mnp_app_id || '未配置'}\n` +
                              `小程序Secret：${data.mnp_secret}\n` +
                              `发货同步开关：${data.express_send_sync ? '开启' : '关闭'}\n` +
                              `微信应用状态：${data.wechat_app_status}\n` +
                              `AccessToken：${data.access_token}`;
                
                showResult('config-result', message, 'success');
            } else {
                showResult('config-result', `检查失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('微信订单API修复验证工具已加载');
        });
    </script>
</body>
</html>

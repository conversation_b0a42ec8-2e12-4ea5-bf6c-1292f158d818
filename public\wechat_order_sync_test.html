<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信订单双向同步测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .sync-direction {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .direction-card {
            flex: 1;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .direction-card.active {
            border-color: #4CAF50;
            background-color: #f8fff8;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .test-button.danger {
            background-color: #f44336;
        }
        .test-button.warning {
            background-color: #ff9800;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .config-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .callback-url {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 微信订单双向同步测试</h1>
        <p>此工具用于测试微信小程序订单状态的双向同步功能</p>
        
        <div class="warning result">
            <strong>重要说明：</strong><br>
            1. 项目端 → 微信端：用户在项目中确认收货，同步状态到微信小程序后台<br>
            2. 微信端 → 项目端：用户在微信收到发货通知后确认收货，微信回调更新项目订单状态<br>
            3. 需要在微信小程序后台配置回调URL才能接收微信的确认收货通知
        </div>
    </div>

    <div class="container">
        <h2>⚙️ 回调配置</h2>
        <div class="config-section">
            <h3>微信小程序后台配置</h3>
            <p>请在微信小程序后台的"功能" → "订单与物流" → "订单管理"中配置以下回调URL：</p>
            
            <div class="callback-url">
                <strong>确认收货回调URL：</strong><br>
                <span id="callback-url">https://您的域名/api/wechat/order/confirm-receive</span>
            </div>
            
            <button class="test-button" onclick="copyCallbackUrl()">复制回调URL</button>
            <button class="test-button" onclick="testCallbackUrl()">测试回调URL</button>
            <div id="callback-test-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📋 订单信息</h2>
        <div class="form-group">
            <label for="order-id">订单ID:</label>
            <input type="number" id="order-id" placeholder="请输入微信支付的订单ID">
        </div>
        <button class="test-button" onclick="loadOrderInfo()">加载订单信息</button>
        <div id="order-info-result" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>🔄 双向同步测试</h2>
        
        <div class="sync-direction">
            <div class="direction-card">
                <h3>📱 项目端 → 微信端</h3>
                <p>用户在项目中确认收货<br>同步状态到微信小程序后台</p>
                <button class="test-button" onclick="testProjectToWechat()">测试项目→微信同步</button>
                <div id="project-to-wechat-result" class="result" style="display:none;"></div>
            </div>
            
            <div class="direction-card">
                <h3>💬 微信端 → 项目端</h3>
                <p>用户在微信端确认收货<br>微信回调更新项目状态</p>
                <button class="test-button" onclick="simulateWechatCallback()">模拟微信回调</button>
                <div id="wechat-to-project-result" class="result" style="display:none;"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 手动同步工具</h2>
        <div class="form-group">
            <label for="sync-direction">同步方向:</label>
            <select id="sync-direction">
                <option value="to_wechat">同步到微信</option>
                <option value="from_wechat">从微信同步</option>
            </select>
        </div>
        <button class="test-button" onclick="manualSync()">手动同步</button>
        <button class="test-button warning" onclick="getSyncStatus()">查看同步状态</button>
        <div id="manual-sync-result" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>📊 同步状态监控</h2>
        <button class="test-button" onclick="startMonitoring()">开始监控</button>
        <button class="test-button danger" onclick="stopMonitoring()">停止监控</button>
        <div id="monitoring-result" class="result" style="display:none;"></div>
    </div>

    <script>
        let currentOrderId = '';
        let monitoringInterval = null;

        // 显示结果的通用函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // 发送API请求的通用函数
        async function sendRequest(url, data = {}, method = 'POST') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }

        // 设置回调URL
        function setCallbackUrl() {
            const domain = window.location.origin;
            document.getElementById('callback-url').textContent = `${domain}/api/wechat/order/confirm-receive`;
        }

        // 复制回调URL
        function copyCallbackUrl() {
            const url = document.getElementById('callback-url').textContent;
            navigator.clipboard.writeText(url).then(() => {
                showResult('callback-test-result', '回调URL已复制到剪贴板', 'success');
            });
        }

        // 测试回调URL
        async function testCallbackUrl() {
            showResult('callback-test-result', '正在测试回调URL...', 'info');
            
            const testData = {
                order_key: {
                    order_number_type: 2,
                    transaction_id: 'test_transaction_id'
                },
                received_time: new Date().toISOString()
            };

            const result = await sendRequest('/api/wechat/order/confirm-receive', testData);
            
            if (result.code === 1) {
                showResult('callback-test-result', '回调URL测试成功', 'success');
            } else {
                showResult('callback-test-result', `回调URL测试失败：${result.msg}`, 'error');
            }
        }

        // 加载订单信息
        async function loadOrderInfo() {
            const orderId = document.getElementById('order-id').value;
            if (!orderId) {
                showResult('order-info-result', '请输入订单ID', 'error');
                return;
            }

            currentOrderId = orderId;
            showResult('order-info-result', '正在加载订单信息...', 'info');

            const result = await sendRequest('/api/wechat/order/sync-status', { order_id: orderId });

            if (result.code === 1) {
                const data = result.data;
                const info = `订单同步状态：
订单ID: ${data.order_id}
订单状态: ${data.order_status}
发货状态: ${data.shipping_status}
微信同步状态: ${data.wechat_mini_express_sync}
微信同步时间: ${data.wechat_mini_express_sync_time ? new Date(data.wechat_mini_express_sync_time * 1000).toLocaleString() : '未同步'}
确认收货时间: ${data.confirm_take_time ? new Date(data.confirm_take_time * 1000).toLocaleString() : '未确认'}
是否微信支付: ${data.is_wechat_pay ? '是' : '否'}`;

                showResult('order-info-result', info, 'success');
            } else {
                showResult('order-info-result', `加载失败：${result.msg}`, 'error');
            }
        }

        // 测试项目到微信的同步
        async function testProjectToWechat() {
            if (!currentOrderId) {
                showResult('project-to-wechat-result', '请先加载订单信息', 'error');
                return;
            }

            showResult('project-to-wechat-result', '正在测试项目→微信同步...', 'info');

            const result = await sendRequest('/api/wechat/order/manual-sync', { 
                order_id: currentOrderId,
                direction: 'to_wechat'
            });

            if (result.code === 1) {
                const message = `同步结果：
${JSON.stringify(result.data, null, 2)}`;
                showResult('project-to-wechat-result', message, 'success');
            } else {
                showResult('project-to-wechat-result', `同步失败：${result.msg}`, 'error');
            }
        }

        // 模拟微信回调
        async function simulateWechatCallback() {
            if (!currentOrderId) {
                showResult('wechat-to-project-result', '请先加载订单信息', 'error');
                return;
            }

            showResult('wechat-to-project-result', '正在模拟微信回调...', 'info');

            // 这里需要先获取订单的transaction_id
            const orderInfo = await sendRequest('/debug/confirm', { order_id: currentOrderId });
            
            if (orderInfo.code !== 1) {
                showResult('wechat-to-project-result', '获取订单信息失败', 'error');
                return;
            }

            const callbackData = {
                order_key: {
                    order_number_type: 2,
                    transaction_id: orderInfo.data.final_transaction_id
                },
                received_time: new Date().toISOString()
            };

            const result = await sendRequest('/api/wechat/order/confirm-receive', callbackData);

            if (result.code === 1) {
                showResult('wechat-to-project-result', '微信回调模拟成功，订单状态已更新', 'success');
            } else {
                showResult('wechat-to-project-result', `微信回调模拟失败：${result.msg}`, 'error');
            }
        }

        // 手动同步
        async function manualSync() {
            if (!currentOrderId) {
                showResult('manual-sync-result', '请先加载订单信息', 'error');
                return;
            }

            const direction = document.getElementById('sync-direction').value;
            showResult('manual-sync-result', '正在执行手动同步...', 'info');

            const result = await sendRequest('/api/wechat/order/manual-sync', { 
                order_id: currentOrderId,
                direction: direction
            });

            if (result.code === 1) {
                const message = `手动同步完成：
${JSON.stringify(result.data, null, 2)}`;
                showResult('manual-sync-result', message, 'success');
            } else {
                showResult('manual-sync-result', `手动同步失败：${result.msg}`, 'error');
            }
        }

        // 查看同步状态
        async function getSyncStatus() {
            if (!currentOrderId) {
                showResult('manual-sync-result', '请先加载订单信息', 'error');
                return;
            }

            const result = await sendRequest('/api/wechat/order/sync-status', { order_id: currentOrderId });

            if (result.code === 1) {
                const data = result.data;
                const message = `同步状态详情：
${JSON.stringify(data, null, 2)}`;
                showResult('manual-sync-result', message, 'info');
            } else {
                showResult('manual-sync-result', `获取状态失败：${result.msg}`, 'error');
            }
        }

        // 开始监控
        function startMonitoring() {
            if (!currentOrderId) {
                showResult('monitoring-result', '请先加载订单信息', 'error');
                return;
            }

            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }

            showResult('monitoring-result', '开始监控订单同步状态...', 'info');

            monitoringInterval = setInterval(async () => {
                const result = await sendRequest('/api/wechat/order/sync-status', { order_id: currentOrderId });
                
                if (result.code === 1) {
                    const data = result.data;
                    const timestamp = new Date().toLocaleTimeString();
                    const status = `[${timestamp}] 订单状态: ${data.order_status}, 微信同步: ${data.wechat_mini_express_sync}, 确认收货: ${data.confirm_take_time ? '已确认' : '未确认'}`;
                    
                    const element = document.getElementById('monitoring-result');
                    element.textContent = status + '\n' + element.textContent;
                }
            }, 5000); // 每5秒检查一次
        }

        // 停止监控
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                showResult('monitoring-result', '监控已停止', 'warning');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            setCallbackUrl();
            console.log('微信订单双向同步测试工具已加载');
        });
    </script>
</body>
</html>

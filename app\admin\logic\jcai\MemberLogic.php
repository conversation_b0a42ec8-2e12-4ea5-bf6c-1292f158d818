<?php

namespace app\admin\logic\jcai;

use app\common\basics\Logic;
use app\common\model\jcai\JcaiOrder;
use app\common\server\UrlServer;
use Exception;

class MemberLogic extends Logic
{
    /**
     * @Notes: 获取集采会员订单列表
     * @Author: Cline
     * @param $get
     * @return array|bool
     */
    public static function lists($get)
    {
        try {
            $where = [];
            if (!empty($get['datetime']) && $get['datetime']) {
                list($start, $end) = explode(' - ', $get['datetime']);
                if ($start && $end) {
                    $where[] = ['O.create_time', '>=', strtotime($start . ' 00:00:00')];
                    $where[] = ['O.create_time', '<=', strtotime($end . ' 23:59:59')];
                }
            }

            if (!empty($get['order_sn']) && $get['order_sn']) {
                $where[] = ['O.order_sn', 'like', '%' . $get['order_sn'] . '%'];
            }

            if (isset($get['pay_status']) && $get['pay_status'] !== '' && is_numeric($get['pay_status'])) {
                $where[] = ['O.pay_status', '=', (int)$get['pay_status']];
            }

            $model = new JcaiOrder();
            $lists = $model->alias('O')
                ->field(['O.*', 'U.nickname', 'U.sn as user_sn', 'U.avatar'])
                ->join('user U', 'U.id = O.user_id')
                ->where($where)
                ->order('O.create_time desc')
                ->paginate($get['limit'] ?? 20, false, [
                    'page' => $get['page'] ?? 1,
                    'var_page' => 'page'
                ])->toArray();

            foreach ($lists['data'] as &$item) {
                $item['pay_time_text'] = !empty($item['pay_time']) ? date('Y-m-d H:i:s', $item['pay_time']) : '';
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['pay_status_text'] = $item['pay_status'] == 1 ? '已支付' : '待支付';
                $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            }
            return ['count' => $lists['total'], 'lists' => $lists['data']];
        } catch (Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @Notes: 统计
     * @Author: Cline
     * @return array
     */
    public static function statistics()
    {
        $model = new JcaiOrder();
        $total = $model->count();
        $paid = $model->where('pay_status', 1)->count();
        $unpaid = $model->where('pay_status', 0)->count();
        return compact('total', 'paid', 'unpaid');
    }
}

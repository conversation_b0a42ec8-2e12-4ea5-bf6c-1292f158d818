<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\common\enum\OrderEnum;
use app\common\enum\PayEnum;
use app\common\logic\OrderLogLogic;
use app\common\model\order\Order;
use app\common\model\order\OrderTrade;
use app\common\server\JsonServer;
use app\common\server\WechatMiniExpressSendSyncServer;
use think\facade\Log;

/**
 * 微信小程序订单回调处理器
 * Class WechatOrderCallback
 * @package app\api\controller
 */
class WechatOrderCallback extends Api
{
    public $like_not_need_login = ['confirmReceive', 'orderStatusSync'];

    /**
     * @notes 微信确认收货回调
     * 当用户在微信端点击确认收货时，微信会调用此接口
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function confirmReceive()
    {
        try {
            // 获取微信回调数据
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            Log::write("收到微信确认收货回调：" . $input, 'wechat_order_callback');

            if (empty($data)) {
                Log::write("微信确认收货回调数据为空", 'wechat_order_callback');
                return JsonServer::error('回调数据为空');
            }

            // 验证必要参数
            if (empty($data['order_key']) || empty($data['order_key']['transaction_id'])) {
                Log::write("微信确认收货回调缺少必要参数：" . json_encode($data), 'wechat_order_callback');
                return JsonServer::error('缺少必要参数');
            }

            $transaction_id = $data['order_key']['transaction_id'];
            $received_time = $data['received_time'] ?? time();

            // 根据微信交易号查找订单
            $order = $this->findOrderByTransactionId($transaction_id);
            
            if (!$order) {
                Log::write("根据微信交易号未找到订单：{$transaction_id}", 'wechat_order_callback');
                return JsonServer::error('订单不存在');
            }

            // 检查订单状态
            if ($order['order_status'] == OrderEnum::ORDER_STATUS_COMPLETE) {
                Log::write("订单已完成，无需重复处理：{$order['id']}", 'wechat_order_callback');
                return JsonServer::success('订单已完成');
            }

            if ($order['shipping_status'] == 0) {
                Log::write("订单未发货，无法确认收货：{$order['id']}", 'wechat_order_callback');
                return JsonServer::error('订单未发货');
            }

            // 更新订单状态为已完成
            $updateData = [
                'order_status' => OrderEnum::ORDER_STATUS_COMPLETE,
                'confirm_take_time' => is_numeric($received_time) ? $received_time : strtotime($received_time),
                'update_time' => time(),
            ];

            $result = Order::where('id', $order['id'])->update($updateData);

            if ($result) {
                // 记录订单日志
                OrderLogLogic::record(
                    \app\common\enum\OrderLogEnum::TYPE_SYSTEM,
                    \app\common\enum\OrderLogEnum::SYSTEM_CONFIRM_ORDER,
                    $order['id'],
                    0,
                    '微信端用户确认收货'
                );

                Log::write("微信确认收货回调处理成功，订单ID：{$order['id']}", 'wechat_order_callback');
                return JsonServer::success('处理成功');
            } else {
                Log::write("更新订单状态失败，订单ID：{$order['id']}", 'wechat_order_callback');
                return JsonServer::error('更新订单状态失败');
            }

        } catch (\Exception $e) {
            Log::write("微信确认收货回调异常：" . $e->getMessage() . "\n" . $e->getTraceAsString(), 'wechat_order_callback');
            return JsonServer::error('处理异常');
        }
    }

    /**
     * @notes 订单状态双向同步
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function orderStatusSync()
    {
        try {
            $orderId = $this->request->post('order_id');
            $action = $this->request->post('action'); // 'confirm_receive', 'delivery'
            
            if (empty($orderId) || empty($action)) {
                return JsonServer::error('参数不完整');
            }

            $order = Order::where('id', $orderId)->find();
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();

            // 只处理微信支付订单
            if ($orderData['pay_way'] != PayEnum::WECHAT_PAY) {
                return JsonServer::error('非微信支付订单');
            }

            switch ($action) {
                case 'confirm_receive':
                    // 同步确认收货到微信
                    $result = WechatMiniExpressSendSyncServer::_sync_order_confirm($orderData);
                    break;
                case 'delivery':
                    // 同步发货信息到微信
                    $result = WechatMiniExpressSendSyncServer::_sync_order($orderData);
                    break;
                default:
                    return JsonServer::error('不支持的操作');
            }

            if ($result) {
                return JsonServer::success('同步成功');
            } else {
                return JsonServer::error('同步失败');
            }

        } catch (\Exception $e) {
            Log::write("订单状态同步异常：" . $e->getMessage(), 'wechat_order_callback');
            return JsonServer::error('同步异常');
        }
    }

    /**
     * @notes 根据微信交易号查找订单
     * @param string $transaction_id
     * @return array|null
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    private function findOrderByTransactionId($transaction_id)
    {
        // 先从订单表直接查找
        $order = Order::where('transaction_id', $transaction_id)
            ->where('del', 0)
            ->find();

        if ($order) {
            return $order->toArray();
        }

        // 从订单交易表查找
        $orderTrade = OrderTrade::where('transaction_id', $transaction_id)->find();
        if ($orderTrade) {
            $order = Order::where('id', $orderTrade['order_id'])
                ->where('del', 0)
                ->find();
            
            if ($order) {
                return $order->toArray();
            }
        }

        return null;
    }

    /**
     * @notes 验证微信签名（可选，增强安全性）
     * @param array $data
     * @return bool
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    private function verifyWechatSignature($data)
    {
        // 这里可以添加微信签名验证逻辑
        // 根据微信文档实现签名验证
        return true;
    }

    /**
     * @notes 手动触发订单状态同步
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function manualSync()
    {
        try {
            $orderId = $this->request->post('order_id');
            $direction = $this->request->post('direction', 'to_wechat'); // to_wechat, from_wechat
            
            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            $order = Order::where('id', $orderId)->find();
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();

            if ($orderData['pay_way'] != PayEnum::WECHAT_PAY) {
                return JsonServer::error('非微信支付订单');
            }

            $result = [];

            if ($direction === 'to_wechat') {
                // 同步到微信
                if ($orderData['shipping_status'] > 0) {
                    $deliveryResult = WechatMiniExpressSendSyncServer::_sync_order($orderData);
                    $result['delivery_sync'] = $deliveryResult ? '成功' : '失败';
                }

                if ($orderData['order_status'] == OrderEnum::ORDER_STATUS_COMPLETE) {
                    $confirmResult = WechatMiniExpressSendSyncServer::_sync_order_confirm($orderData);
                    $result['confirm_sync'] = $confirmResult ? '成功' : '失败';
                }
            }

            return JsonServer::success('手动同步完成', $result);

        } catch (\Exception $e) {
            Log::write("手动同步异常：" . $e->getMessage(), 'wechat_order_callback');
            return JsonServer::error('同步异常');
        }
    }

    /**
     * @notes 获取订单微信同步状态
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024-01-01 00:00:00
     */
    public function getSyncStatus()
    {
        try {
            $orderId = $this->request->post('order_id');
            
            if (empty($orderId)) {
                return JsonServer::error('订单ID不能为空');
            }

            $order = Order::where('id', $orderId)->find();
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            $orderData = $order->toArray();

            $status = [
                'order_id' => $orderId,
                'order_status' => $orderData['order_status'],
                'shipping_status' => $orderData['shipping_status'],
                'wechat_mini_express_sync' => $orderData['wechat_mini_express_sync'] ?? 0,
                'wechat_mini_express_sync_time' => $orderData['wechat_mini_express_sync_time'] ?? 0,
                'confirm_take_time' => $orderData['confirm_take_time'] ?? 0,
                'is_wechat_pay' => $orderData['pay_way'] == PayEnum::WECHAT_PAY,
            ];

            return JsonServer::success('获取成功', $status);

        } catch (\Exception $e) {
            return JsonServer::error('获取失败');
        }
    }
}

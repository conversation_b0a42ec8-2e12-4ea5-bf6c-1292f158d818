# 微信订单API对接实现文档

## 概述

本文档描述了kshop项目中微信小程序订单发货、确认收货、退货API对接功能的完整实现。

## 实现功能

### ✅ 已完成功能

1. **微信订单发货API对接**
   - 实物商品发货时自动调用微信发货接口
   - 支持快递配送、线下自提、虚拟发货三种配送方式
   - 包含物流信息同步（快递公司、运单号等）

2. **虚拟商品自动发货逻辑**
   - 虚拟商品付款成功后自动调用微信发货接口
   - 无需物流信息，直接标记为虚拟发货

3. **微信确认收货API对接**
   - 用户手动确认收货时调用微信确认收货接口
   - 商家代为确认收货时同步微信状态

4. **自动确认收货机制**
   - 实物商品发货后到达自动确认时间时调用微信确认收货接口
   - 支持配置自动确认天数（默认7天）

5. **微信退货API对接**
   - 订单退货时调用微信退货接口
   - 同步退货状态和退款信息到微信端

6. **商品类型区分逻辑**
   - 完善的实物商品和虚拟商品区分机制
   - 不同类型商品走不同的发货和确认流程

## 技术实现

### 核心文件修改

#### 1. 微信API服务类扩展
**文件**: `app/common/server/WechatMiniExpressSendSyncServer.php`

新增方法：
- `_sync_order_confirm()` - 确认收货信息录入
- `_sync_order_refund()` - 退货退款信息录入

#### 2. 商家端订单逻辑
**文件**: `app/shopapi/logic/OrderLogic.php`

修改内容：
- `delivery()` - 发货时调用微信API
- `virtualDelivery()` - 虚拟发货时调用微信API  
- `confirm()` - 确认收货时调用微信API

#### 3. 用户端订单逻辑
**文件**: `app/api/logic/OrderLogic.php`

修改内容：
- `confirm()` - 用户确认收货时调用微信API

#### 4. 虚拟商品逻辑
**文件**: `app/common/logic/GoodsVirtualLogic.php`

修改内容：
- `afterPayVirtualDelivery()` - 虚拟商品自动发货时调用微信API

#### 5. 退款逻辑
**文件**: `app/common/logic/OrderRefundLogic.php`

修改内容：
- `addRefundLog()` - 退款成功时调用微信API

#### 6. 自动确认收货命令
**文件**: `app/common/command/OrderFinish.php`

修改内容：
- 自动确认收货时调用微信API

### 微信API接口说明

#### 1. 发货信息录入接口
```
POST /wxa/sec/order/upload_shipping_info
```

支持的物流类型：
- `1` - 快递发货
- `3` - 虚拟发货  
- `4` - 门店自提

#### 2. 确认收货通知接口
```
POST /wxa/sec/order/notify_confirm_receive
```

#### 3. 退款通知接口
```
POST /wxa/sec/order/notify_refund
```

### 业务流程

#### 发货流程
1. 商家在后台发货
2. 系统更新订单状态为"待收货"
3. 自动调用微信发货API同步状态
4. 发送发货通知给用户

#### 确认收货流程
1. 用户确认收货或系统自动确认
2. 系统更新订单状态为"已完成"
3. 自动调用微信确认收货API
4. 订单交易完成

#### 退货流程
1. 用户申请退货或商家主动退款
2. 系统处理退款逻辑
3. 自动调用微信退货API同步状态
4. 退款完成

## 测试功能

### 测试页面
访问地址：`/test_wechat_order_api.html`

### 测试接口
- `POST /test/auto-confirm` - 测试自动确认收货
- `POST /test/wechat-sync-check` - 查询微信同步状态
- `POST /test/delivery-sync` - 测试发货同步
- `POST /test/confirm-sync` - 测试确认收货同步
- `POST /test/refund-sync` - 测试退款同步
- `GET /test/orders` - 获取测试订单列表

### 测试控制器
**文件**: `app/api/controller/WechatOrderTest.php`

## 配置说明

### 微信小程序配置
确保在后台配置了正确的微信小程序信息：
- `app_id` - 小程序AppID
- `secret` - 小程序AppSecret
- `mch_id` - 商户号
- `key` - 商户密钥

### 发货同步开关
在系统配置中可以控制是否开启微信发货同步：
```php
ConfigServer::get('mnp', 'express_send_sync', 1)
```

## 数据库字段

### 订单表新增字段
- `wechat_mini_express_sync` - 微信发货同步状态（0-未录入，1-已录入，2-录入失败）
- `wechat_mini_express_sync_time` - 微信发货录入时间

### 其他相关表
类似字段也添加到了以下表：
- `ls_recharge_order` - 充值订单表
- `ls_ad_order` - 广告订单表
- `ls_shop_deposit` - 商家保证金表
- `ls_shop_merchantfees` - 商家入驻费表
- `ls_agent_merchantfees` - 代理保证金表
- `ls_integral_order` - 积分订单表
- `ls_jcai_order` - 集采购会员订单表

## 定时任务

### 微信发货信息同步
**命令**: `php think wechat_mini_express_send_sync`

该命令会自动同步以下类型的订单：
- 普通订单（快递、自提、虚拟发货）
- 充值订单
- 广告订单
- 商家保证金订单
- 商家入驻费订单
- 代理保证金订单
- 积分订单
- 集采购会员订单

### 自动确认收货
**命令**: `php think order_finish`

自动确认超过指定天数的待收货订单，并同步微信状态。

## 错误处理

### 日志记录
所有微信API调用错误都会记录到日志文件：
- `wechat_sync_error` - 微信同步错误日志
- `wechat_mini_express_sync_*` - 各类型订单同步日志

### 容错机制
- 微信API调用失败不会影响正常的业务流程
- 支持重试机制（通过定时任务）
- Token失效时会等待下次执行

## 注意事项

1. **微信支付订单才会同步**：只有使用微信支付的订单才会调用微信API
2. **用户授权**：用户需要在小程序中授权才能获取openid
3. **电商类目**：小程序需要开通电商类目才能使用订单管理功能
4. **API限制**：注意微信API的调用频率限制

## 部署说明

1. 确保服务器可以访问微信API
2. 配置正确的微信小程序信息
3. 设置定时任务执行微信同步命令
4. 测试各个功能是否正常工作

## 联系支持

如有问题，请查看：
1. 微信开发者文档
2. 系统日志文件
3. 测试页面的错误信息

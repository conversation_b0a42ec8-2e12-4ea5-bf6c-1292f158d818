<?php


namespace app\common\logic;


use app\common\enum\GoodsEnum;
use app\common\enum\OrderEnum;
use app\common\enum\PayEnum;
use app\common\model\goods\Goods;
use app\common\model\order\Order;
use app\common\server\WechatMiniExpressSendSyncServer;

/**
 * 虚拟商品逻辑
 * Class GoodsVirtualLogic
 * @package app\common\logic
 */
class GoodsVirtualLogic
{

    /**
     * @notes 订单之后虚拟配送
     * @param $orderIds
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/4/12 11:37
     */
    public static function afterPayVirtualDelivery($orderIds)
    {
        $orderIds = is_array($orderIds) ? $orderIds : [$orderIds];
        $orders = Order::with(['order_goods'])->whereIn('id', $orderIds)->select()->toArray();

        foreach ($orders as $order) {

            $goodsId = $order['order_goods'][0]['goods_id'] ?? 0;
            $goods = Goods::findOrEmpty($goodsId);

            // 商品不存在,不是虚拟商品,买家付款后不是自动发货
            if ($goods->isEmpty() || $goods['type'] != GoodsEnum::TYPE_VIRTUAL) {
                continue;
            }

            $data = [
                'delivery_content' => $goods['delivery_content'],
                'update_time' => time(),
            ];

            // 商品为支付后自动发货
            if ($goods['after_pay'] == GoodsEnum::AFTER_PAY_AUTO_DELIVERY) {
                $data['order_status'] = OrderEnum::ORDER_STATUS_GOODS; // 待收货状态
                $data['delivery_type'] = OrderEnum::DELIVERY_TYPE_VIRTUAL; // 发货方式为虚拟发货
                $data['shipping_status'] = OrderEnum::SHIPPING_FINISH; // 已发货
                $data['shipping_time'] = time();

                // 自动完成订单
                if ($goods['after_delivery'] == GoodsEnum::AFTER_DELIVERY_AUTO_COMFIRM) {
                    $data['order_status'] = OrderEnum::ORDER_STATUS_COMPLETE;// 已完成
                    $data['confirm_take_time'] = time();
                }
            }
            Order::where(['id' => $order['id']])->update($data);

            // 如果是虚拟商品自动发货，同步微信小程序发货信息
            if (isset($data['shipping_status']) && $data['shipping_status'] == OrderEnum::SHIPPING_FINISH && $order['pay_way'] == PayEnum::WECHAT_PAY) {
                try {
                    $updatedOrder = Order::where('id', $order['id'])->find()->toArray();
                    WechatMiniExpressSendSyncServer::_sync_order($updatedOrder);
                } catch (\Exception $e) {
                    // 记录日志但不影响发货流程
                    \think\facade\Log::write('微信虚拟商品自动发货信息同步失败: ' . $e->getMessage(), 'wechat_sync_error');
                }
            }
        }

        return true;
    }



    /**
     * @notes 商家手动发货
     * @param $orderId
     * @param null $content
     * @return bool|string
     * <AUTHOR>
     * @date 2022/4/12 11:44
     */
    public static function shopSelfDelivery($orderId, $content = null)
    {
        $order = Order::with(['order_goods'])->where('id', $orderId)->findOrEmpty()->toArray();

        $goodsId = $order['order_goods'][0]['goods_id'] ?? 0;
        $goods = Goods::findOrEmpty($goodsId);

        // 商品不存在,不是虚拟商品,买家付款后不是自动发货
        if ($goods->isEmpty() || $goods['type'] != GoodsEnum::TYPE_VIRTUAL) {
            return '虚拟商品信息不存在';
        }

        $data = [
            'order_status' => OrderEnum::ORDER_STATUS_GOODS, // 待收货状态
            'delivery_type' => OrderEnum::DELIVERY_TYPE_VIRTUAL, // 发货方式为虚拟发货
            'delivery_content' => empty($content) ? $goods['delivery_content'] : $content,
            'shipping_status' => OrderEnum::SHIPPING_FINISH, // 已发货
            'shipping_time' => time(),
            'update_time' => time(),
        ];

        // 自动完成订单
        if ($goods['after_delivery'] == GoodsEnum::AFTER_DELIVERY_AUTO_COMFIRM) {
            $data['order_status'] = OrderEnum::ORDER_STATUS_COMPLETE;// 已完成
            $data['confirm_take_time'] = time();
        }
        Order::where(['id' => $order['id']])->update($data);
        return true;
    }


}
<?php

namespace app\common\server;

use app\common\enum\OrderEnum;
use app\common\model\Client_;
use app\common\model\Delivery;
use app\common\model\Express;
use app\common\model\order\Order;
use app\common\model\order\OrderGoods;
use app\common\model\order\OrderTrade;
use app\common\model\RechargeOrder;
use app\common\model\user\UserAuth;
use app\common\model\AdOrder;
use app\common\model\shop\ShopDeposit;
use app\common\model\shop\ShopMerchantfees;
use app\common\model\agent\AgentMerchantfees;
use app\common\model\integral\IntegralOrder;
use app\common\model\jcai\JcaiOrder;
use EasyWeChat\Factory;
use Psr\SimpleCache\InvalidArgumentException;
use think\facade\Log;
use think\helper\Str;

/**
 * @notes 小程序 发货信息录入
 * @notes https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/order-shipping/order-shipping.html
 * author lbzy
 * @datetime 2023-07-14 14:51:23
 * @class WechatMiniExpressSendSyncService
 * @package app\common\service
 */
class WechatMiniExpressSendSyncServer
{
    static private $miniApp;

    private static function getMiniApp()
    {
        static::$miniApp = static::$miniApp ? : Factory::miniProgram(WeChatServer::getMnpConfig());

        return static::$miniApp;
    }

    /**
     * @notes 订单录入
     * @param array $order
     * @return bool
     * <AUTHOR>
     * @datetime 2023-07-14 14:34:05
     */
    static function _sync_order(array $order) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_order';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $order['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$order['user_id']} openid不存在,订单ID：{$order['id']}", $log);
                Order::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => time() ], [
                    [ 'id', '=', $order['id'] ],
                ]);
                return false;
            }

            $order_goods = OrderGoods::where('order_id', $order['id'])->select()->toArray();
            $goods_names = implode(" ", array_column($order_goods, 'goods_name'));

            $shipping_item = [
                'item_desc' => mb_strlen($goods_names) > 128 ? (mb_substr($goods_names, 0, 124, 'UTF-8') . '...') : $goods_names,
            ];

            $data = [
                'order_key'     => [
                    'order_number_type'     => 2,
                    'transaction_id'        => $order['transaction_id'] ? : OrderTrade::where('id', $order['trade_id'])->value('transaction_id', ''),
                ],
                'delivery_mode' => 1,
                'upload_time'   => date(DATE_RFC3339, $time),
                'payer'         => [
                    'openid'    => $user->openid,
                ],
            ];

            $delivery   = Delivery::where('order_id', $order['id'])->findOrEmpty();
            $express    = Express::where('id', $delivery['shipping_id'] ?? 0)->findOrEmpty();

            if (! empty($express->code) && ! empty($delivery->invoice_no) && ! empty($delivery->mobile)) {
                $shipping_item['tracking_no']                   = $delivery->invoice_no;
                // 微信小程序 物流公司delivery_id
                $shipping_item['express_company']               = $express->code;
                $shipping_item['contact']['receiver_contact']   = substr_replace($delivery->mobile, '****', 3, 4);
            } else {
                // 无需物流 改为门店自提
                $data['logistics_type'] = 4;
            }

            $data['shipping_list'][] = $shipping_item;

            switch ($order['delivery_type']) {
                // 快递发货
                case OrderEnum::DELIVERY_TYPE_EXPRESS:
                    $data['logistics_type'] = $data['logistics_type'] ?? 1;
                    break;
                // 门店自提
                case OrderEnum::DELIVERY_TYPE_SELF:
                    $data['logistics_type'] = 4;
                    break;
                // 虚拟发货
                case OrderEnum::DELIVERY_TYPE_VIRTUAL:
                    $data['logistics_type'] = 3;
                    break;
                default:
                    break;
            }

            // dump($data);

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/upload_shipping_info?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            // dump($result);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "发货录入发生错误", $log);
                Order::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => $time ], [
                    [ 'id', '=', $order['id'] ],
                ]);
                return false;
            }

            Order::update([ 'wechat_mini_express_sync' => 1, 'wechat_mini_express_sync_time' => $time ], [
                [ 'id', '=', $order['id'] ],
            ]);

            return true;

        } catch(\Throwable $e) {
            // dump($e->__toString());
            Log::write($e->__toString(), $log);
            return false;
        }
    }

    /**
     * @notes 充值录入
     * @param array $recharge
     * @return bool
     * @throws InvalidArgumentException
     * <AUTHOR>
     * @datetime 2023-07-17 15:18:18
     */
    static function _sync_recharge(array $recharge) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_recharge';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $recharge['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$recharge['user_id']} openid不存在,订单ID：{$recharge['id']}", $log);
                RechargeOrder::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => time() ], [
                    [ 'id', '=', $recharge['id'] ],
                ]);
                return false;
            }

            $shipping_item = [
                'item_desc' => '余额充值',
            ];

            $data = [
                'order_key'     => [
                    'order_number_type'     => 2,
                    'transaction_id'        => $recharge['transaction_id'],
                ],
                'delivery_mode' => 1,
                'upload_time'   => date(DATE_RFC3339, $time),
                'payer'         => [
                    'openid'    => $user->openid,
                ],
                'logistics_type' => 3,
            ];

            $data['shipping_list'][] = $shipping_item;

            // dump($data);

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/upload_shipping_info?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            // dump($result);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "发货录入发生错误", $log);
                RechargeOrder::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => $time ], [
                    [ 'id', '=', $recharge['id'] ],
                ]);
                return false;
            }

            RechargeOrder::update([ 'wechat_mini_express_sync' => 1, 'wechat_mini_express_sync_time' => $time ], [
                [ 'id', '=', $recharge['id'] ],
            ]);

            return true;

        } catch(\Throwable $e) {
            // dump($e->__toString());
            Log::write($e->__toString(), $log);
            return false;
        }
    }

    private static function getToken($app)
    {
        return $app->access_token->getToken()['access_token'];
    }

    static function wechatSyncCheck($order)
    {
        $app    = static::getMiniApp();

        $data = [
            'transaction_id' => $order['transaction_id'] ?? '',
        ];

        $token = static::getToken($app);

        $result_content = $app->http_client->post("/wxa/sec/order/get_order?access_token={$token}", [
            'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
        ])->getBody()->getContents();

        $result = json_decode($result_content, true);
        if (! isset($result['errcode']) || $result['errcode'] != 0) {
            // token失效 不标记失败 等下次执行
            if (isset($result['errcode']) && $result['errcode'] == 40001) {
                Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), 'wechat_mini_express_sync_check');
                return false;
            }
            Log::write($result_content ? : "发货录入发生错误", 'wechat_mini_express_sync_check');
            return [];
        }

        return $result;

    }

    /**
     * @notes 广告订单录入
     * @param array $adOrder
     * @return bool
     * <AUTHOR>
     * @datetime 2024-01-01 00:00:00
     */
    static function _sync_ad_order(array $adOrder) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_ad_order';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $adOrder['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$adOrder['user_id']} openid不存在,广告订单ID：{$adOrder['id']}", $log);
                AdOrder::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => time() ], [
                    [ 'id', '=', $adOrder['id'] ],
                ]);
                return false;
            }

            $shipping_item = [
                'item_desc' => '商家购买广告费',
            ];

            $data = [
                'order_key'     => [
                    'order_number_type'     => 1,
                    'mchid'                 => WeChatServer::getMnpConfig()['payment']['merchant_id'] ?? '',
                    'out_trade_no'          => $adOrder['order_sn'],
                ],
                'delivery_mode' => 1,
                'upload_time'   => date(DATE_RFC3339, $time),
                'payer'         => [
                    'openid'    => $user->openid,
                ],
                'logistics_type' => 3, // 虚拟发货
            ];

            $data['shipping_list'][] = $shipping_item;

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/upload_shipping_info?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "发货录入发生错误", $log);
                AdOrder::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => $time ], [
                    [ 'id', '=', $adOrder['id'] ],
                ]);
                return false;
            }

            AdOrder::update([ 'wechat_mini_express_sync' => 1, 'wechat_mini_express_sync_time' => $time ], [
                [ 'id', '=', $adOrder['id'] ],
            ]);

            return true;

        } catch(\Throwable $e) {
            Log::write($e->__toString(), $log);
            return false;
        }
    }

    /**
     * @notes 商家保证金录入
     * @param array $shopDeposit
     * @return bool
     * <AUTHOR>
     * @datetime 2024-01-01 00:00:00
     */
    static function _sync_shop_deposit(array $shopDeposit) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_shop_deposit';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $shopDeposit['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$shopDeposit['user_id']} openid不存在,保证金订单ID：{$shopDeposit['id']}", $log);
                ShopDeposit::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => time() ], [
                    [ 'id', '=', $shopDeposit['id'] ],
                ]);
                return false;
            }

            $item_desc = $shopDeposit['type'] == 0 ? '商家缴纳保证金' : '商家补缴保证金';
            $shipping_item = [
                'item_desc' => $item_desc,
            ];

            $data = [
                'order_key'     => [
                    'order_number_type'     => 2,
                    'transaction_id'        => $shopDeposit['transaction_id'] ?? '',
                ],
                'delivery_mode' => 1,
                'upload_time'   => date(DATE_RFC3339, $time),
                'payer'         => [
                    'openid'    => $user->openid,
                ],
                'logistics_type' => 3, // 虚拟发货
            ];

            $data['shipping_list'][] = $shipping_item;

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/upload_shipping_info?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "发货录入发生错误", $log);
                ShopDeposit::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => $time ], [
                    [ 'id', '=', $shopDeposit['id'] ],
                ]);
                return false;
            }

            ShopDeposit::update([ 'wechat_mini_express_sync' => 1, 'wechat_mini_express_sync_time' => $time ], [
                [ 'id', '=', $shopDeposit['id'] ],
            ]);

            return true;

        } catch(\Throwable $e) {
            Log::write($e->__toString(), $log);
            return false;
        }
    }

    /**
     * @notes 商家入驻费录入
     * @param array $shopMerchantfees
     * @return bool
     * <AUTHOR>
     * @datetime 2024-01-01 00:00:00
     */
    static function _sync_shop_merchantfees(array $shopMerchantfees) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_shop_merchantfees';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $shopMerchantfees['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$shopMerchantfees['user_id']} openid不存在,入驻费订单ID：{$shopMerchantfees['id']}", $log);
                ShopMerchantfees::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => time() ], [
                    [ 'id', '=', $shopMerchantfees['id'] ],
                ]);
                return false;
            }

            $item_desc = '商家入驻费';
            if ($shopMerchantfees['feetype'] == 1) {
                $item_desc = '商家高级入驻费(含验厂)';
            } elseif ($shopMerchantfees['feetype'] == 2) {
                $item_desc = '商家验厂费';
            }

            $shipping_item = [
                'item_desc' => $item_desc,
            ];

            $data = [
                'order_key'     => [
                    'order_number_type'     => 1,
                    'mchid'                 => WeChatServer::getMnpConfig()['payment']['merchant_id'] ?? '',
                    'out_trade_no'          => $shopMerchantfees['order_sn'],
                ],
                'delivery_mode' => 1,
                'upload_time'   => date(DATE_RFC3339, $time),
                'payer'         => [
                    'openid'    => $user->openid,
                ],
                'logistics_type' => 3, // 虚拟发货
            ];

            $data['shipping_list'][] = $shipping_item;

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/upload_shipping_info?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "发货录入发生错误", $log);
                ShopMerchantfees::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => $time ], [
                    [ 'id', '=', $shopMerchantfees['id'] ],
                ]);
                return false;
            }

            ShopMerchantfees::update([ 'wechat_mini_express_sync' => 1, 'wechat_mini_express_sync_time' => $time ], [
                [ 'id', '=', $shopMerchantfees['id'] ],
            ]);

            return true;

        } catch(\Throwable $e) {
            Log::write($e->__toString(), $log);
            return false;
        }
    }

    /**
     * @notes 代理保证金录入
     * @param array $agentMerchantfees
     * @return bool
     * <AUTHOR>
     * @datetime 2024-01-01 00:00:00
     */
    static function _sync_agent_merchantfees(array $agentMerchantfees) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_agent_merchantfees';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $agentMerchantfees['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$agentMerchantfees['user_id']} openid不存在,代理保证金订单ID：{$agentMerchantfees['id']}", $log);
                AgentMerchantfees::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => time() ], [
                    [ 'id', '=', $agentMerchantfees['id'] ],
                ]);
                return false;
            }

            $shipping_item = [
                'item_desc' => '代理保证金',
            ];

            $data = [
                'order_key'     => [
                    'order_number_type'     => 2,
                    'transaction_id'        => $agentMerchantfees['transaction_id'] ?? '',
                ],
                'delivery_mode' => 1,
                'upload_time'   => date(DATE_RFC3339, $time),
                'payer'         => [
                    'openid'    => $user->openid,
                ],
                'logistics_type' => 3, // 虚拟发货
            ];

            $data['shipping_list'][] = $shipping_item;

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/upload_shipping_info?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "发货录入发生错误", $log);
                AgentMerchantfees::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => $time ], [
                    [ 'id', '=', $agentMerchantfees['id'] ],
                ]);
                return false;
            }

            AgentMerchantfees::update([ 'wechat_mini_express_sync' => 1, 'wechat_mini_express_sync_time' => $time ], [
                [ 'id', '=', $agentMerchantfees['id'] ],
            ]);

            return true;

        } catch(\Throwable $e) {
            Log::write($e->__toString(), $log);
            return false;
        }
    }

    /**
     * @notes 积分订单录入
     * @param array $integralOrder
     * @return bool
     * <AUTHOR>
     * @datetime 2024-01-01 00:00:00
     */
    static function _sync_integral_order(array $integralOrder) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_integral_order';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $integralOrder['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$integralOrder['user_id']} openid不存在,积分订单ID：{$integralOrder['id']}", $log);
                IntegralOrder::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => time() ], [
                    [ 'id', '=', $integralOrder['id'] ],
                ]);
                return false;
            }

            $shipping_item = [
                'item_desc' => '积分商品兑换',
            ];

            $data = [
                'order_key'     => [
                    'order_number_type'     => 2,
                    'transaction_id'        => $integralOrder['transaction_id'] ?? '',
                ],
                'delivery_mode' => 1,
                'upload_time'   => date(DATE_RFC3339, $time),
                'payer'         => [
                    'openid'    => $user->openid,
                ],
                'logistics_type' => 3, // 虚拟发货
            ];

            $data['shipping_list'][] = $shipping_item;

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/upload_shipping_info?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "发货录入发生错误", $log);
                IntegralOrder::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => $time ], [
                    [ 'id', '=', $integralOrder['id'] ],
                ]);
                return false;
            }

            IntegralOrder::update([ 'wechat_mini_express_sync' => 1, 'wechat_mini_express_sync_time' => $time ], [
                [ 'id', '=', $integralOrder['id'] ],
            ]);

            return true;

        } catch(\Throwable $e) {
            Log::write($e->__toString(), $log);
            return false;
        }
    }

    /**
     * @notes 集采购会员订单录入
     * @param array $jcaiOrder
     * @return bool
     * <AUTHOR>
     * @datetime 2024-01-01 00:00:00
     */
    static function _sync_jcai_order(array $jcaiOrder) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_jcai_order';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $jcaiOrder['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$jcaiOrder['user_id']} openid不存在,集采购会员订单ID：{$jcaiOrder['id']}", $log);
                JcaiOrder::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => time() ], [
                    [ 'id', '=', $jcaiOrder['id'] ],
                ]);
                return false;
            }

            $shipping_item = [
                'item_desc' => '用户购买集采购会员',
            ];

            $data = [
                'order_key'     => [
                    'order_number_type'     => 2,
                    'transaction_id'        => $jcaiOrder['transaction_id'] ?? '',
                ],
                'delivery_mode' => 1,
                'upload_time'   => date(DATE_RFC3339, $time),
                'payer'         => [
                    'openid'    => $user->openid,
                ],
                'logistics_type' => 3, // 虚拟发货
            ];

            $data['shipping_list'][] = $shipping_item;

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/upload_shipping_info?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "发货录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "发货录入发生错误", $log);
                JcaiOrder::update([ 'wechat_mini_express_sync' => 2, 'wechat_mini_express_sync_time' => $time ], [
                    [ 'id', '=', $jcaiOrder['id'] ],
                ]);
                return false;
            }

            JcaiOrder::update([ 'wechat_mini_express_sync' => 1, 'wechat_mini_express_sync_time' => $time ], [
                [ 'id', '=', $jcaiOrder['id'] ],
            ]);

            return true;

        } catch(\Throwable $e) {
            Log::write($e->__toString(), $log);
            return false;
        }
    }

    /**
     * @notes 确认收货信息录入
     * @param array $order
     * @return bool
     * <AUTHOR>
     * @datetime 2024-01-01 00:00:00
     */
    static function _sync_order_confirm(array $order) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_order_confirm';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $order['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$order['user_id']} openid不存在,订单ID：{$order['id']}", $log);
                return false;
            }

            // 检查订单是否已发货（必须先发货才能确认收货）
            if (empty($order['wechat_mini_express_sync']) || $order['wechat_mini_express_sync'] != 1) {
                Log::write("订单ID：{$order['id']} 尚未同步发货信息到微信，无法确认收货", $log);
                return false;
            }

            // 获取微信支付交易号
            $transaction_id = '';
            if (!empty($order['transaction_id'])) {
                $transaction_id = $order['transaction_id'];
            } elseif (!empty($order['trade_id'])) {
                $transaction_id = OrderTrade::where('id', $order['trade_id'])->value('transaction_id', '');
            }

            if (empty($transaction_id)) {
                Log::write("订单ID：{$order['id']} 微信交易号不存在，无法同步确认收货", $log);
                return false;
            }

            // 确保有确认收货时间
            $confirm_time = $order['confirm_take_time'] ?? $time;
            if (empty($confirm_time)) {
                $confirm_time = $time;
            }

            // 根据微信文档构建请求参数
            $data = [
                'order_key' => [
                    'order_number_type' => 2, // 使用微信支付交易号
                    'transaction_id' => $transaction_id,
                ],
                'received_time' => date('c', $confirm_time), // ISO 8601格式 (RFC3339)
            ];

            Log::write("准备同步确认收货信息：" . json_encode($data, JSON_UNESCAPED_UNICODE), $log);

            $token = static::getToken($app);

            // 使用正确的请求头
            $result_content = $app->http_client->post("https://api.weixin.qq.com/wxa/sec/order/notify_confirm_receive?access_token={$token}", [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            Log::write("微信API返回结果：" . $result_content, $log);

            if (!isset($result['errcode']) || $result['errcode'] != 0) {
                // 根据错误码处理
                $errcode = $result['errcode'] ?? -1;
                $errmsg = $result['errmsg'] ?? '未知错误';

                switch ($errcode) {
                    case 40001:
                        Log::write("AccessToken失效，等待下次执行：{$errmsg}", $log);
                        return false;
                    case 9300501:
                        Log::write("订单不存在或状态不正确：{$errmsg}", $log);
                        return false;
                    case 9300502:
                        Log::write("订单尚未发货，无法确认收货：{$errmsg}", $log);
                        return false;
                    case 9300503:
                        Log::write("订单已确认收货：{$errmsg}", $log);
                        return true; // 已确认收货算成功
                    default:
                        Log::write("确认收货同步失败，错误码：{$errcode}，错误信息：{$errmsg}", $log);
                        return false;
                }
            }

            Log::write("订单ID：{$order['id']} 确认收货信息同步成功", $log);
            return true;

        } catch(\Throwable $e) {
            Log::write("确认收货同步异常：" . $e->getMessage() . "\n" . $e->getTraceAsString(), $log);
            return false;
        }
    }

    /**
     * @notes 退货退款信息录入
     * @param array $order
     * @param array $refundInfo
     * @return bool
     * <AUTHOR>
     * @datetime 2024-01-01 00:00:00
     */
    static function _sync_order_refund(array $order, array $refundInfo) : bool
    {
        try {
            $time   = time();
            $log    = 'wechat_mini_express_sync_order_refund';
            $app    = static::getMiniApp();
            $user   = UserAuth::where('user_id', $order['user_id'])->where('client', Client_::mnp)->findOrEmpty();

            if (empty($user->openid)) {
                Log::write("用户：{$order['user_id']} openid不存在,订单ID：{$order['id']}", $log);
                return false;
            }

            $data = [
                'order_key'     => [
                    'order_number_type'     => 2,
                    'transaction_id'        => $order['transaction_id'] ? : OrderTrade::where('id', $order['trade_id'])->value('transaction_id', ''),
                ],
                'action_type'   => 2, // 退款
                'action_time'   => date(DATE_RFC3339, $refundInfo['refund_time'] ?? $time),
                'refund_fee'    => intval($refundInfo['refund_amount'] * 100), // 退款金额，单位为分
                'reason'        => $refundInfo['refund_reason'] ?? '用户申请退款',
            ];

            $token = static::getToken($app);

            $result_content = $app->http_client->post("/wxa/sec/order/notify_refund?access_token={$token}", [
                'body'   => json_encode($data, JSON_UNESCAPED_UNICODE),
            ])->getBody()->getContents();

            $result = json_decode($result_content, true);

            if (! isset($result['errcode']) || $result['errcode'] != 0) {
                // token失效 不标记失败 等下次执行
                if (isset($result['errcode']) && $result['errcode'] == 40001) {
                    Log::write("等待下次执行" . ($result_content ? : "退货退款录入发生错误"), $log);
                    return false;
                }
                Log::write($result_content ? : "退货退款录入发生错误", $log);
                return false;
            }

            Log::write("订单ID：{$order['id']} 退货退款信息同步成功", $log);
            return true;

        } catch(\Throwable $e) {
            Log::write($e->__toString(), $log);
            return false;
        }
    }
}
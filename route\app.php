<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Config;
use think\facade\Route;


// 手机h5页面路由
Route::rule('mobile/:any', function () {
    $isOpen = \app\common\server\ConfigServer::get('h5', 'is_open', 1);
    if(!$isOpen) {
        return '';
    }
    Config::set(['app_trace' => false]);
    return view(app()->getRootPath() . 'public/mobile/index.html');
})->pattern(['any' => '\w+']);


// PC商城端
Route::rule('pc/:any', function () {
    $isOpen = \app\common\server\ConfigServer::get('pc', 'is_open', 1);
    if(!$isOpen) {
        return '';
    }
    Config::set(['app_trace' => false]);
    return view(app()->getRootPath() . 'public/pc/index.html');
})->pattern(['any' => '\w+']);


// 商家移动端
Route::rule('business/:any', function () {
    Config::set(['app_trace' => false]);
    return view(app()->getRootPath() . 'public/business/index.html');
})->pattern(['any' => '\w+']);


// 客服
Route::rule('kefu/:any', function () {
    Config::set(['app_trace' => false]);
    return view(app()->getRootPath() . 'public/kefu/index.html');
})->pattern(['any' => '\w+']);

// 微信订单API测试路由
Route::group('test', function () {
    Route::post('auto-confirm', 'api/WechatOrderTest/autoConfirm');
    Route::post('wechat-sync-check', 'api/WechatOrderTest/wechatSyncCheck');
    Route::post('delivery-sync', 'api/WechatOrderTest/testDeliverySync');
    Route::post('confirm-sync', 'api/WechatOrderTest/testConfirmSync');
    Route::post('refund-sync', 'api/WechatOrderTest/testRefundSync');
    Route::get('orders', 'api/WechatOrderTest/getTestOrders');
});

// 微信订单调试路由
Route::group('debug', function () {
    Route::post('confirm', 'api/OrderDebug/debugConfirm');
    Route::post('delivery', 'api/OrderDebug/debugDelivery');
    Route::post('manual-confirm-sync', 'api/OrderDebug/manualConfirmSync');
    Route::get('wechat-config', 'api/OrderDebug/checkWechatConfig');
});


// 客服移动端
Route::rule('kefu_m/:any', function () {
    Config::set(['app_trace' => false]);
    return view(app()->getRootPath() . 'public/kefu_m/index.html');
})->pattern(['any' => '\w+']);

//定时任务
Route::rule('crontab', function () {
    \think\facade\Console::call('crontab');
});

// 用户对用户聊天API路由
Route::group('api/user_chat', function () {
    Route::get('chat_list', 'api/UserChat/chatList');
    Route::get('chat_history', 'api/UserChat/chatHistory');
    Route::get('search_user', 'api/UserChat/searchUser');
})->middleware(\app\api\http\middleware\Login::class);

// 聊天配置检查路由
Route::group('api/chat', function () {
    Route::post('checkConfig', 'api/ChatController/checkConfig');
    Route::post('getConfigDetail', 'api/ChatController/getConfigDetail');
    Route::post('testConnectionParams', 'api/ChatController/testConnectionParams');
});

// 聊天记录接口测试路由
Route::group('api/chat_record_test', function () {
    Route::get('new_chat_record', 'api/ChatRecordTest/testNewChatRecord');
    Route::get('user_chat', 'api/ChatRecordTest/testUserChat');
    Route::get('kefu_chat', 'api/ChatRecordTest/testKefuChat');
    Route::get('empty_params', 'api/ChatRecordTest/testEmptyParams');
})->middleware(\app\api\http\middleware\Login::class);

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信订单API对接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button.danger {
            background-color: #f44336;
        }
        .test-button.danger:hover {
            background-color: #da190b;
        }
        .test-button.warning {
            background-color: #ff9800;
        }
        .test-button.warning:hover {
            background-color: #e68900;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 微信订单API对接测试工具</h1>
        <p>此工具用于测试kshop项目中微信小程序订单发货、确认收货、退货等API对接功能的正确性。</p>
    </div>

    <div class="container">
        <h2>📋 测试概览</h2>
        <div class="test-section">
            <h3>已实现的功能</h3>
            <ul>
                <li><span class="status-indicator status-success"></span>微信订单发货API对接（实物商品）</li>
                <li><span class="status-indicator status-success"></span>微信虚拟商品自动发货</li>
                <li><span class="status-indicator status-success"></span>微信确认收货API对接</li>
                <li><span class="status-indicator status-success"></span>微信自动确认收货机制</li>
                <li><span class="status-indicator status-success"></span>微信退货API对接</li>
                <li><span class="status-indicator status-success"></span>商品类型区分逻辑</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🚀 发货测试</h2>
        <div class="test-section">
            <h3>实物商品发货测试</h3>
            <div class="form-group">
                <label for="order-id-delivery">订单ID:</label>
                <input type="number" id="order-id-delivery" placeholder="请输入订单ID">
            </div>
            <div class="form-group">
                <label for="express-company">物流公司:</label>
                <select id="express-company">
                    <option value="1">顺丰速运</option>
                    <option value="2">圆通速递</option>
                    <option value="3">中通快递</option>
                    <option value="4">韵达速递</option>
                    <option value="5">申通快递</option>
                </select>
            </div>
            <div class="form-group">
                <label for="tracking-number">快递单号:</label>
                <input type="text" id="tracking-number" placeholder="请输入快递单号">
            </div>
            <button class="test-button" onclick="testDelivery()">测试实物商品发货</button>
            <div id="delivery-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>虚拟商品发货测试</h3>
            <div class="form-group">
                <label for="virtual-order-id">订单ID:</label>
                <input type="number" id="virtual-order-id" placeholder="请输入虚拟商品订单ID">
            </div>
            <div class="form-group">
                <label for="delivery-content">发货内容:</label>
                <textarea id="delivery-content" placeholder="请输入虚拟商品发货内容，如：激活码、下载链接等"></textarea>
            </div>
            <button class="test-button" onclick="testVirtualDelivery()">测试虚拟商品发货</button>
            <div id="virtual-delivery-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>✅ 确认收货测试</h2>
        <div class="test-section">
            <h3>用户确认收货测试</h3>
            <div class="form-group">
                <label for="confirm-order-id">订单ID:</label>
                <input type="number" id="confirm-order-id" placeholder="请输入已发货的订单ID">
            </div>
            <button class="test-button" onclick="testConfirmReceive()">测试用户确认收货</button>
            <div id="confirm-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>自动确认收货测试</h3>
            <div class="form-group">
                <label for="auto-confirm-days">自动确认天数:</label>
                <input type="number" id="auto-confirm-days" value="7" placeholder="默认7天">
            </div>
            <button class="test-button warning" onclick="testAutoConfirm()">模拟自动确认收货</button>
            <div id="auto-confirm-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔄 退货退款测试</h2>
        <div class="test-section">
            <h3>订单退款测试</h3>
            <div class="form-group">
                <label for="refund-order-id">订单ID:</label>
                <input type="number" id="refund-order-id" placeholder="请输入需要退款的订单ID">
            </div>
            <div class="form-group">
                <label for="refund-amount">退款金额:</label>
                <input type="number" id="refund-amount" step="0.01" placeholder="请输入退款金额">
            </div>
            <div class="form-group">
                <label for="refund-reason">退款原因:</label>
                <input type="text" id="refund-reason" placeholder="请输入退款原因">
            </div>
            <button class="test-button danger" onclick="testRefund()">测试订单退款</button>
            <div id="refund-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔍 微信同步状态查询</h2>
        <div class="test-section">
            <h3>查询订单微信同步状态</h3>
            <div class="form-group">
                <label for="check-order-id">订单ID:</label>
                <input type="number" id="check-order-id" placeholder="请输入订单ID">
            </div>
            <button class="test-button" onclick="checkWechatSyncStatus()">查询微信同步状态</button>
            <div id="check-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // 显示结果的通用函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // 发送API请求的通用函数
        async function sendRequest(url, data = {}) {
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }

        // 测试实物商品发货
        async function testDelivery() {
            const orderId = document.getElementById('order-id-delivery').value;
            const expressCompany = document.getElementById('express-company').value;
            const trackingNumber = document.getElementById('tracking-number').value;

            if (!orderId || !trackingNumber) {
                showResult('delivery-result', '请填写完整的订单信息', 'error');
                return;
            }

            showResult('delivery-result', '正在测试实物商品发货...', 'info');

            const result = await sendRequest('/shopapi/order/delivery', {
                id: orderId,
                send_type: 1,
                shipping_id: expressCompany,
                invoice_no: trackingNumber
            });

            if (result.code === 1) {
                showResult('delivery-result', `发货成功！\n微信API同步状态：${result.wechat_sync ? '已同步' : '同步中'}`, 'success');
            } else {
                showResult('delivery-result', `发货失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 测试虚拟商品发货
        async function testVirtualDelivery() {
            const orderId = document.getElementById('virtual-order-id').value;
            const content = document.getElementById('delivery-content').value;

            if (!orderId || !content) {
                showResult('virtual-delivery-result', '请填写完整的订单信息', 'error');
                return;
            }

            showResult('virtual-delivery-result', '正在测试虚拟商品发货...', 'info');

            const result = await sendRequest('/shopapi/order/virtualDelivery', {
                order_id: orderId,
                delivery_content: content
            });

            if (result.code === 1) {
                showResult('virtual-delivery-result', `虚拟商品发货成功！\n微信API同步状态：${result.wechat_sync ? '已同步' : '同步中'}`, 'success');
            } else {
                showResult('virtual-delivery-result', `虚拟商品发货失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 测试确认收货
        async function testConfirmReceive() {
            const orderId = document.getElementById('confirm-order-id').value;

            if (!orderId) {
                showResult('confirm-result', '请输入订单ID', 'error');
                return;
            }

            showResult('confirm-result', '正在测试确认收货...', 'info');

            const result = await sendRequest('/api/order/confirm', {
                id: orderId
            });

            if (result.code === 1) {
                showResult('confirm-result', `确认收货成功！\n微信API同步状态：${result.wechat_sync ? '已同步' : '同步中'}`, 'success');
            } else {
                showResult('confirm-result', `确认收货失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 测试自动确认收货
        async function testAutoConfirm() {
            const days = document.getElementById('auto-confirm-days').value || 7;

            showResult('auto-confirm-result', '正在模拟自动确认收货...', 'info');

            const result = await sendRequest('/test/auto-confirm', {
                days: days
            });

            if (result.code === 1) {
                showResult('auto-confirm-result', `自动确认收货测试完成！\n处理订单数：${result.count || 0}\n微信API同步状态：${result.wechat_sync ? '已同步' : '同步中'}`, 'success');
            } else {
                showResult('auto-confirm-result', `自动确认收货测试失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 测试退款
        async function testRefund() {
            const orderId = document.getElementById('refund-order-id').value;
            const amount = document.getElementById('refund-amount').value;
            const reason = document.getElementById('refund-reason').value;

            if (!orderId || !amount) {
                showResult('refund-result', '请填写完整的退款信息', 'error');
                return;
            }

            showResult('refund-result', '正在测试订单退款...', 'info');

            const result = await sendRequest('/api/order/refund', {
                order_id: orderId,
                refund_amount: amount,
                refund_reason: reason || '用户申请退款'
            });

            if (result.code === 1) {
                showResult('refund-result', `退款申请成功！\n微信API同步状态：${result.wechat_sync ? '已同步' : '同步中'}`, 'success');
            } else {
                showResult('refund-result', `退款申请失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 查询微信同步状态
        async function checkWechatSyncStatus() {
            const orderId = document.getElementById('check-order-id').value;

            if (!orderId) {
                showResult('check-result', '请输入订单ID', 'error');
                return;
            }

            showResult('check-result', '正在查询微信同步状态...', 'info');

            const result = await sendRequest('/api/order/wechatSyncCheck', {
                id: orderId
            });

            if (result.code === 1) {
                const data = result.data;
                const statusText = data.wechat_mini_express_sync === 1 ? '已同步' : 
                                 data.wechat_mini_express_sync === 2 ? '同步失败' : '未同步';
                
                showResult('check-result', 
                    `订单微信同步状态：${statusText}\n` +
                    `同步时间：${data.wechat_mini_express_sync_time ? new Date(data.wechat_mini_express_sync_time * 1000).toLocaleString() : '未同步'}\n` +
                    `订单状态：${data.order_status_text}\n` +
                    `配送方式：${data.delivery_type_text}`, 
                    'success'
                );
            } else {
                showResult('check-result', `查询失败：${result.msg || '未知错误'}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('微信订单API测试工具已加载');
        });
    </script>
</body>
</html>

<?php
namespace app\api\controller;

use app\api\logic\LoginLogic;
use app\common\basics\Api;
use app\api\logic\UserLogic;
use app\common\enum\NoticeEnum;
use app\common\server\JsonServer;
use app\api\validate\UpdateUserValidate;
use app\api\validate\SetWechatUserValidate;
use app\api\validate\WechatMobileValidate;
use app\api\validate\ChangeMobileValidate;
use think\exception\ValidateException;

class  User extends Api
{
    public $like_not_need_login = ['bankList','getChatUserList'];
    /***
     * 个人中心
     */
    public function center()
    {
        $config = UserLogic::center($this->user_id);
        if($config<0){
            return JsonServer::error('登录过期',[],401);
        }
        return JsonServer::success('', $config);
    }

    /**
     * 用户信息
     */
    public function info()
    {
        return JsonServer::success('', UserLogic::getUserInfo($this->user_id));
    }

    /**
     * Notes:设置用户信息
     */
    public function setInfo()
    {
        try{
            $post = $this->request->post();
            $post['user_id'] = $this->user_id;
            validate(UpdateUserValidate::class)->scene('set')->check($post);
        }catch(ValidateException $e) {
            return JsonServer::error($e->getError());
        }
        $result = UserLogic::setUserInfo($post);
        if($result === true) {
            return JsonServer::success('设置成功');
        }
        return JsonServer::error(UserLogic::getError());
    }



    /**
     * 财户流水
     */
    public function accountLog(){
        // 来源类型 1-余额 2-积分 3-成长值
        $source = $this->request->get('source', '');
        if(empty($source)) {
            return JsonServer::error('请传入来源类型');
        }
        // 变动类型
        $type = $this->request->get('type');
        $data = UserLogic::accountLog($this->user_id, $source,$type, $this->page_no, $this->page_size);
        return JsonServer::success('', $data);
    }

    /***
     * 会员中心 - 会员等级
     */
    public function getUserLevelInfo() {
        $data = UserLogic::getUserLevelInfo($this->user_id);
        return JsonServer::success('', $data);
    }


    /**
     * 成长值记录
     */
    public function getGrowthList()
    {
        $get = $this->request->get();
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $get['user_id'] = $this->user_id;
        $data = UserLogic::getGrowthList($get);
        return JsonServer::success('', $data);
    }

    /**
     * 我的钱包
     */
    public function myWallet(){
        $result = UserLogic::myWallet($this->user_id);
        if($result === false) {
            return JsonServer::error(UserLogic::getError());
        }
        return JsonServer::success('获取成功', $result);
    }

    /**
     * Notes: 更新微信的用户信息
     */
    public function setWechatInfo()
    {
        $data = $this->request->post();
        try{
            validate(SetWechatUserValidate::class)->check($data);
        }catch(ValidateException $e) {
            return JsonServer::error($e->getError());
        }
        $result = UserLogic::updateWechatInfo($this->user_id, $data);
        if($result === true) {
            return JsonServer::success('更新成功');
        }
        return JsonServer::error(UserLogic::getError());
    }

    //获取微信手机号
    public function getMobile()
    {
        try{
            $post = $this->request->post();
            $post['user_id'] = $this->user_id;
            validate(WechatMobileValidate::class)->check($post);
        }catch(ValidateException $e) {
            return JsonServer::error($e->getError());
        }
        $result = UserLogic::getMobileByMnp($post);
        if($result === false) {
            return JsonServer::error(UserLogic::getError());
        }
        return JsonServer::success('操作成功', [],1,1);
    }



    /**
     * Notes: 更换手机号 / 绑定手机号
     * <AUTHOR>
     * @return \think\response\Json
     */
    public function changeMobile()
    {
        $data = $this->request->post();
        $data['client'] = $this->client;
        $data['user_id'] = $this->user_id;
        if(isset($data['action']) && 'change' == $data['action']) {
            //变更手机号码
            $data['message_key'] = NoticeEnum::CHANGE_MOBILE_NOTICE;
            (new ChangeMobileValidate())->goCheck('', $data);
        } else {
            //绑定手机号码
            $data['message_key'] = NoticeEnum::BIND_MOBILE_NOTICE;
            (new ChangeMobileValidate())->goCheck('binding', $data);
        }
        $result = UserLogic::changeMobile($this->user_id, $data);
        if(false === $result) {
            return JsonServer::error(UserLogic::getError());
        }
        if(is_object($result)){
            $result = $result->toArray();
        }
        return JsonServer::success('操作成功',$result);
    }

    //我的粉丝
    public function fans()
    {
        $get = $this->request->get();
        $page = $this->request->get('page_no', $this->page_no);
        $size = $this->request->get('page_size', $this->page_size);
        return JsonServer::success('', UserLogic::fans($this->user_id, $get, $page, $size));
    }

    /**
     * @notes 用户聊天记录
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/12/20 11:29
     */
    public function chatRecord()
    {
        $shop_id = $this->request->get('shop_id/d', 0);
        $to_id=$this->request->get('to_id/d', 0);
        $result = UserLogic::getChatRecord($this->user_id,$to_id,$shop_id, $this->page_no, $this->page_size);
        return JsonServer::success('', $result);
    }

    /**
     * @notes 用户聊天记录
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/12/20 11:29
     */
    public function getChatUserList()
    {
        $this->user_id=$this->user_id??0;
        $get = $this->request->get();
        $get['page_no'] =$get['page_no']??$this->page_no;
        $get['page_size'] =$get['page_size']??15;
        $result = UserLogic::getChatUserList($this->user_id, $get, $get['page_no'],  $get['page_size']);
        return JsonServer::success('', $result);
    }

    /**
     * @notes 删除联系人
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/12/19 11:29
     */
    public function deleteChatUser()
    {
        $post = $this->request->post();
        $kefu_id = $post['kefu_id'] ?? 0;
        $shop_id = $post['shop_id'] ?? 0;

        if (empty($kefu_id) || empty($shop_id)) {
            return JsonServer::error('参数错误');
        }

        $result = UserLogic::deleteChatUser($this->user_id, $kefu_id, $shop_id);
        if ($result) {
            return JsonServer::success('删除成功');
        } else {
            return JsonServer::error('删除失败');
        }
    }

    /*
     * 获取用户手机号码
     */
    public function getPhoneNumber()
    {
        $post = $this->request->post();
        $result = LoginLogic::getPhoneNumber($post['code']);
        return JsonServer::success('', $result);
    }


    /*
     * 获取我的用户列表(我是代理)
     */
    public function myuser(){
        $get = $this->request->get();
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $get['user_id'] =$get['user_id']??$this->user_id;
        $data = UserLogic::myuser($get);
        return JsonServer::success('', $data);

    }

    /*
     * 获取我的收益
     */
    public function mymoney(){
        $get = $this->request->get();
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $get['user_id'] = $this->user_id;
        $data = UserLogic::mymoney($get);
        return JsonServer::success('', $data);

    }

    /*
     * 获取我的收益收入明细
     */
    public function mymoneylist(){
        $get = $this->request->get();
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $get['user_id'] = $this->user_id;
        $data = UserLogic::mymoneylist($get);
        return JsonServer::success('', $data);

    }

    /*
   * 获取我的收益支出明细
   */
    public function mymoneyapplylist(){
        $get = $this->request->get();
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $get['user_id'] = $this->user_id;
        $data = UserLogic::mymoneyapplylist($get);
        return JsonServer::success('', $data);

    }

    //佣金提现
    public function applyWithdraw(){
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        if(empty($post['user_id'])){
            return JsonServer::error('缺少参数');
        }
        UserLogic::applyWithdraw($post);

        return JsonServer::success('提现成功');
    }

    //添加银行卡
    public function addBankCard(){

        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
         UserLogic::addBank($post);
        return JsonServer::success('添加成功');
    }

    //获取添加的银行卡
    public function getBankCard(){
        $get = $this->request->get();
        if(empty($get['agent_id'])){
            return JsonServer::error('缺少参数');
        }
        $data = UserLogic::getBankCard($get);
        return JsonServer::success('', $data);
    }

    //添加支付宝账号
    public function addAlipay(){

        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        UserLogic::addAlipay($post);
        return JsonServer::success('添加成功');
    }
    /**
     * @notes 删除银行卡
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 18:42
     */
    public function delBank(){
        $id = $this->request->post('id');
        UserLogic::delBank($id);
        return JsonServer::success('删除成功');
    }

    //添加支付宝账号
    public function getAlipay(){

        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $data = UserLogic::getAlipay($post);
        return JsonServer::success('添加成功',$data);
    }

    //获取支付宝账号
    public function getAlipayInfo(){
        $post = $this->request->get();
        $data = UserLogic::getAlipayInfo($post);
        if(empty($data)){
            return JsonServer::success('',[]);
        }
        $data['bank_logo'] = 'https://www.huohanghang.cn/static/common/image/pay/icon_alipay.png';
        return JsonServer::success('添加成功',$data);
    }


    //获取银行列表
    public function bankList(){
        $data = UserLogic::bankList();
        return JsonServer::success('', $data);
    }

    /**
     * @notes 申请代理保证金退款
     * @return \think\response\Json
     */
    public function applyAgentDepositRefund()
    {
        $post = $this->request->post();
        $result = UserLogic::applyAgentDepositRefund($this->user_id,$post);
        // 检查返回结果是否是数组格式（新格式）
        if (is_array($result) && isset($result['code'])) {
            // 直接返回标准格式的结果
            return json($result);
        } else if ($result === true) {
            // 兼容旧格式
            return JsonServer::success('退款申请已提交，请等待审核');
        } else {
            // 兼容旧格式
            return JsonServer::error($result ?: '申请失败');
        }
    }

    /**
     * @notes 撤销代理保证金退款申请
     * @return \think\response\Json
     */
    public function cancelAgentDepositRefund()
    {
        $result = UserLogic::cancelAgentDepositRefund($this->user_id);
        // 检查返回结果是否是数组格式（新格式）
        if (is_array($result) && isset($result['code'])) {
            // 直接返回标准格式的结果
            return json($result);
        } else if ($result === true) {
            // 兼容旧格式，但由于无法获取状态，统一使用"操作成功"
            return JsonServer::success('操作成功');
        } else {
            // 兼容旧格式
            return JsonServer::error($result ?: '操作失败');
        }
    }




    /**
     * @notes 获取不同类型的用户列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2023/11/15 15:49
     */
    public function getUsersByType()
    {
        $type = $this->request->get('type', 0);
        $page = $this->request->get('page_no', $this->page_no);
        $size = $this->request->get('page_size', $this->page_size);

        // 类型说明
        // 0: 我的用户(自己发展的) - agent_relationships表sponsor_id为自己的用户
        // 1: 我的会员(购买了集采购会员的用户) - agent_order表user_id为自己的所有g_user_id且order_type为1
        // 2: 我的商家(购买了商家入驻费的用户) - agent_order表user_id为自己的所有g_user_id且order_type为2,3,4
        // 3: 我的代理(发展的用户成为代理) - agent_relationships中sponsor_id为自己的所有user_id且is_agent=1

        $data = UserLogic::getUsersByType($this->user_id, $type, $page, $size);
        return JsonServer::success('获取成功', $data);
    }

    /*
     * 我的保证金(代理)
     *
     */
    public function myAgentDeposit(){

        $get = $this->request->get();
        $get['page_no'] =$get['page_no']??$this->page_no;
        $get['page_size'] =$get['page_size']?? $this->page_size;
        $get['user_id'] = $this->user_id;
        $data = UserLogic::myAgentDeposit($get);
        return JsonServer::success('', $data);

    }

    /**
     * @notes 获取代理退款须知
     * @return \think\response\Json
     */
    public function getAgentRefundNotice()
    {
        $data = UserLogic::getAgentRefundNotice();
        return JsonServer::success('获取成功', $data);
    }

    /**
     * @notes 检查代理退款条件
     * @return \think\response\Json
     */
    public function checkAgentRefundCondition()
    {
        $result = UserLogic::checkAgentRefundCondition($this->user_id);
        return json($result);
    }

    /**
     * @notes 获取代理保证金退款申请状态
     * @return \think\response\Json
     */
    public function getAgentRefundStatus()
    {
        $result = UserLogic::getAgentRefundStatus($this->user_id);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * @notes 确认代理保证金退款
     * @return \think\response\Json
     */
    public function confirmAgentRefund()
    {
        $result = UserLogic::confirmAgentRefund($this->user_id);
        if ($result === true) {
            return JsonServer::success('退款申请已提交,请等待1-7个工作日到账,届时请注意查收短信通知.');
        } else {
            return JsonServer::error($result ?: '申请失败');
        }
    }

    /**
     * @notes 检查代理退款状态详情
     * @return \think\response\Json
     */
    public function checkAgentRefundStatus()
    {
        $result = UserLogic::checkAgentRefundStatus($this->user_id);
        // 检查返回结果是否是数组格式（新格式）
        if (is_array($result) && isset($result['code'])) {
            // 直接返回标准格式的结果
            return json($result);
        } else {
            // 兼容旧格式
            return JsonServer::success('检查完成', $result);
        }
    }

    /**
     * @notes 招商顾问资料认证
     * @return \think\response\Json
     */
    public function investmentConsultantAuth()
    {
        $post = $this->request->post();
        $name = $post['name'] ?? '';
        $mobile = $post['mobile'] ?? '';
        $address = $post['address'] ?? '';

        if (empty($name) || empty($mobile) || empty($address)) {
            return JsonServer::error('姓名、手机号和地址不能为空');
        }

        $data = [
            'uid' => $this->user_id,
            'name' => $name,
            'mobile' => $mobile,
            'address' => $address,
            'create_time' => date('Y-m-d H:i:s'),
        ];

        try {
            \think\facade\Db::name('agent_consultant')->insert($data);
            return JsonServer::success('认证资料提交成功');
        } catch (\Exception $e) {
            return JsonServer::error('认证资料提交失败');
        }
    }
}

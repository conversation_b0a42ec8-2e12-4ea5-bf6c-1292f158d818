<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信订单完整流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .flow-step {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            position: relative;
        }
        .step-number {
            position: absolute;
            top: -10px;
            left: 15px;
            background: #4CAF50;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pending { background-color: #ffc107; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .flow-arrow {
            text-align: center;
            font-size: 20px;
            color: #4CAF50;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 微信订单完整流程测试</h1>
        <p>此工具按照微信官方文档要求，测试完整的订单流程：<strong>发货 → 确认收货</strong></p>
        <div class="warning result">
            <strong>重要提醒：</strong>根据微信文档，必须先成功调用发货接口，才能调用确认收货接口！
        </div>
    </div>

    <div class="container">
        <h2>📋 测试订单信息</h2>
        <div class="form-group">
            <label for="test-order-id">订单ID:</label>
            <input type="number" id="test-order-id" placeholder="请输入微信支付的订单ID">
        </div>
        <button class="test-button" onclick="loadOrderInfo()">加载订单信息</button>
        <div id="order-info-result" class="result" style="display:none;"></div>
    </div>

    <div class="container">
        <h2>🚀 完整流程测试</h2>
        
        <div class="flow-step">
            <div class="step-number">1</div>
            <h3>步骤1：检查订单状态</h3>
            <p>检查订单是否为微信支付，是否已发货等基本信息</p>
            <button class="test-button" onclick="checkOrderStatus()" id="step1-btn">检查订单状态</button>
            <div id="step1-result" class="result" style="display:none;"></div>
        </div>

        <div class="flow-arrow">↓</div>

        <div class="flow-step">
            <div class="step-number">2</div>
            <h3>步骤2：同步发货信息到微信</h3>
            <p>调用微信发货接口，上传物流信息</p>
            <button class="test-button" onclick="syncDeliveryToWechat()" id="step2-btn" disabled>同步发货信息</button>
            <div id="step2-result" class="result" style="display:none;"></div>
        </div>

        <div class="flow-arrow">↓</div>

        <div class="flow-step">
            <div class="step-number">3</div>
            <h3>步骤3：模拟用户确认收货</h3>
            <p>更新订单状态为已完成，并同步确认收货信息到微信</p>
            <button class="test-button" onclick="confirmReceiveOrder()" id="step3-btn" disabled>确认收货</button>
            <div id="step3-result" class="result" style="display:none;"></div>
        </div>

        <div class="flow-arrow">↓</div>

        <div class="flow-step">
            <div class="step-number">4</div>
            <h3>步骤4：验证最终状态</h3>
            <p>检查订单最终状态和微信同步结果</p>
            <button class="test-button" onclick="verifyFinalStatus()" id="step4-btn" disabled>验证最终状态</button>
            <div id="step4-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 独立功能测试</h2>
        <div class="flow-step">
            <h3>调试工具</h3>
            <button class="test-button" onclick="debugOrderConfirm()">调试确认收货</button>
            <button class="test-button" onclick="checkWechatConfig()">检查微信配置</button>
            <div id="debug-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        let currentOrderId = '';
        let orderInfo = {};

        // 显示结果的通用函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // 发送API请求的通用函数
        async function sendRequest(url, data = {}, method = 'POST') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (method === 'POST') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }

        // 加载订单信息
        async function loadOrderInfo() {
            const orderId = document.getElementById('test-order-id').value;
            if (!orderId) {
                showResult('order-info-result', '请输入订单ID', 'error');
                return;
            }

            currentOrderId = orderId;
            showResult('order-info-result', '正在加载订单信息...', 'info');

            const result = await sendRequest('/debug/confirm', { order_id: orderId });

            if (result.code === 1) {
                orderInfo = result.data;
                const info = `订单信息加载成功：
订单ID: ${orderInfo.order_id}
订单号: ${orderInfo.order_sn}
支付方式: ${orderInfo.is_wechat_pay ? '微信支付' : '非微信支付'}
订单状态: ${orderInfo.order_status}
发货状态: ${orderInfo.shipping_status}
用户OpenID: ${orderInfo.has_openid ? '已获取' : '未获取'}
微信交易号: ${orderInfo.has_transaction_id ? '已获取' : '未获取'}`;

                showResult('order-info-result', info, 'success');
                
                // 启用第一步按钮
                document.getElementById('step1-btn').disabled = false;
            } else {
                showResult('order-info-result', `加载失败：${result.msg}`, 'error');
            }
        }

        // 步骤1：检查订单状态
        async function checkOrderStatus() {
            if (!currentOrderId) {
                showResult('step1-result', '请先加载订单信息', 'error');
                return;
            }

            showResult('step1-result', '正在检查订单状态...', 'info');

            let checkResult = '';
            let canProceed = true;

            if (!orderInfo.is_wechat_pay) {
                checkResult += '❌ 不是微信支付订单\n';
                canProceed = false;
            } else {
                checkResult += '✅ 微信支付订单\n';
            }

            if (!orderInfo.has_openid) {
                checkResult += '❌ 用户未授权获取OpenID\n';
                canProceed = false;
            } else {
                checkResult += '✅ 用户已授权OpenID\n';
            }

            if (!orderInfo.has_transaction_id) {
                checkResult += '❌ 缺少微信交易号\n';
                canProceed = false;
            } else {
                checkResult += '✅ 微信交易号存在\n';
            }

            if (orderInfo.shipping_status == 0) {
                checkResult += '⚠️ 订单尚未发货\n';
            } else {
                checkResult += '✅ 订单已发货\n';
            }

            if (canProceed) {
                checkResult += '\n✅ 订单状态检查通过，可以进行下一步';
                showResult('step1-result', checkResult, 'success');
                document.getElementById('step2-btn').disabled = false;
            } else {
                checkResult += '\n❌ 订单状态检查失败，无法继续';
                showResult('step1-result', checkResult, 'error');
            }
        }

        // 步骤2：同步发货信息到微信
        async function syncDeliveryToWechat() {
            showResult('step2-result', '正在同步发货信息到微信...', 'info');

            const result = await sendRequest('/debug/delivery', { order_id: currentOrderId });

            if (result.code === 1) {
                const data = result.data;
                const message = `发货同步结果：${data.sync_result}
微信同步状态：${data.wechat_mini_express_sync}
详细信息：${data.sync_detail || '无'}`;

                if (data.sync_result.includes('成功')) {
                    showResult('step2-result', message, 'success');
                    document.getElementById('step3-btn').disabled = false;
                } else {
                    showResult('step2-result', message, 'error');
                }
            } else {
                showResult('step2-result', `同步失败：${result.msg}`, 'error');
            }
        }

        // 步骤3：确认收货
        async function confirmReceiveOrder() {
            showResult('step3-result', '正在执行确认收货流程...', 'info');

            const result = await sendRequest('/debug/test-full-confirm', { 
                order_id: currentOrderId,
                user_id: 1 
            });

            if (result.code === 1) {
                const data = result.data;
                let message = '';
                
                Object.keys(data).forEach(key => {
                    message += `${key}: ${typeof data[key] === 'object' ? JSON.stringify(data[key]) : data[key]}\n`;
                });

                showResult('step3-result', message, 'success');
                document.getElementById('step4-btn').disabled = false;
            } else {
                showResult('step3-result', `确认收货失败：${result.msg}`, 'error');
            }
        }

        // 步骤4：验证最终状态
        async function verifyFinalStatus() {
            showResult('step4-result', '正在验证最终状态...', 'info');

            const result = await sendRequest('/debug/confirm', { order_id: currentOrderId });

            if (result.code === 1) {
                const data = result.data;
                const message = `最终状态验证：
订单状态：${data.order_status}
确认收货时间：${data.confirm_take_time ? new Date(data.confirm_take_time * 1000).toLocaleString() : '未确认'}
微信同步结果：${data.sync_result}
整个流程：${data.sync_result.includes('成功') ? '✅ 完成' : '❌ 失败'}`;

                showResult('step4-result', message, data.sync_result.includes('成功') ? 'success' : 'error');
            } else {
                showResult('step4-result', `验证失败：${result.msg}`, 'error');
            }
        }

        // 调试确认收货
        async function debugOrderConfirm() {
            if (!currentOrderId) {
                showResult('debug-result', '请先加载订单信息', 'error');
                return;
            }

            const result = await sendRequest('/debug/confirm', { order_id: currentOrderId });
            
            if (result.code === 1) {
                const data = result.data;
                const message = `调试信息：
${JSON.stringify(data, null, 2)}`;
                showResult('debug-result', message, 'info');
            } else {
                showResult('debug-result', `调试失败：${result.msg}`, 'error');
            }
        }

        // 检查微信配置
        async function checkWechatConfig() {
            const result = await sendRequest('/debug/wechat-config', {}, 'GET');
            
            if (result.code === 1) {
                const data = result.data;
                const message = `微信配置：
${JSON.stringify(data, null, 2)}`;
                showResult('debug-result', message, 'success');
            } else {
                showResult('debug-result', `配置检查失败：${result.msg}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('微信订单完整流程测试工具已加载');
        });
    </script>
</body>
</html>
